import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../theme/responsive_breakpoints.dart';
import '../theme/design_tokens.dart';
import 'accessibility/focus_traversal_group.dart';
import '../utils/keyboard_shortcuts.dart';

enum NavigationItem {
  home,
  planComptable,
  guide,
  tools,
  references,
  quiz,
  settings,
}

class AdaptiveNavigation extends StatefulWidget {
  final NavigationItem selectedItem;
  final ValueChanged<NavigationItem> onNavigationItemSelected;
  final Widget child;
  final bool enableKeyboardShortcuts;
  final bool enableFocusRestoration;
  final bool enableSkipLinks;
  final bool enableAccessibilityAnnouncements;
  final String? semanticLabel;
  final VoidCallback? onSkipToContent;

  const AdaptiveNavigation({
    required this.selectedItem,
    required this.onNavigationItemSelected,
    required this.child,
    this.enableKeyboardShortcuts = true,
    this.enableFocusRestoration = true,
    this.enableSkipLinks = true,
    this.enableAccessibilityAnnouncements = true,
    this.semanticLabel,
    this.onSkipToContent,
    super.key,
  });

  @override
  State<AdaptiveNavigation> createState() => _AdaptiveNavigationState();
}

class _AdaptiveNavigationState extends State<AdaptiveNavigation> {
  final Map<NavigationItem, FocusNode> _navigationFocusNodes = {};
  final FocusNode _skipLinkFocusNode = FocusNode();
  final GlobalKey _contentKey = GlobalKey();
  late KeyboardShortcutsService _shortcutsService;
  NavigationItem? _lastFocusedItem;

  @override
  void initState() {
    super.initState();
    _initializeFocusNodes();
    _initializeKeyboardShortcuts();
  }

  @override
  void dispose() {
    _disposeFocusNodes();
    _disposeKeyboardShortcuts();
    super.dispose();
  }

  void _initializeFocusNodes() {
    for (final item in NavigationItem.values) {
      _navigationFocusNodes[item] = FocusNode(
        debugLabel: 'Navigation_${item.name}',
      );
    }
    _skipLinkFocusNode.debugLabel = 'SkipToContent';
  }

  void _disposeFocusNodes() {
    for (final focusNode in _navigationFocusNodes.values) {
      focusNode.dispose();
    }
    _skipLinkFocusNode.dispose();
  }

  void _initializeKeyboardShortcuts() {
    if (!widget.enableKeyboardShortcuts) return;
    
    _shortcutsService = KeyboardShortcutsService();
    _shortcutsService.setContext('navigation');
    
    // Register navigation shortcuts
    _shortcutsService.registerCallback(AppShortcut.home, () => _navigateToItem(NavigationItem.home));
    _shortcutsService.registerCallback(AppShortcut.guides, () => _navigateToItem(NavigationItem.guide));
    _shortcutsService.registerCallback(AppShortcut.quiz, () => _navigateToItem(NavigationItem.quiz));
    _shortcutsService.registerCallback(AppShortcut.tools, () => _navigateToItem(NavigationItem.tools));
  }

  void _disposeKeyboardShortcuts() {
    if (!widget.enableKeyboardShortcuts) return;
    
    _shortcutsService.unregisterCallback(AppShortcut.home);
    _shortcutsService.unregisterCallback(AppShortcut.guides);
    _shortcutsService.unregisterCallback(AppShortcut.quiz);
    _shortcutsService.unregisterCallback(AppShortcut.tools);
  }

  void _navigateToItem(NavigationItem item) {
    widget.onNavigationItemSelected(item);
    _focusNavigationItem(item);
    _announceNavigation(item);
  }

  void _focusNavigationItem(NavigationItem item) {
    final focusNode = _navigationFocusNodes[item];
    if (focusNode != null) {
      focusNode.requestFocus();
      _lastFocusedItem = item;
    }
  }

  void _announceNavigation(NavigationItem item) {
    if (!widget.enableAccessibilityAnnouncements) return;
    
    final label = _getNavigationItemLabel(item);
    SemanticsService.announce(
      'Navigation vers $label',
      TextDirection.ltr,
    );
  }

  String _getNavigationItemLabel(NavigationItem item) {
    switch (item) {
      case NavigationItem.home:
        return 'Accueil';
      case NavigationItem.planComptable:
        return 'Plan Comptable Général';
      case NavigationItem.guide:
        return 'Guide';
      case NavigationItem.tools:
        return 'Outils';
      case NavigationItem.references:
        return 'Références';
      case NavigationItem.quiz:
        return 'Quiz';
      case NavigationItem.settings:
        return 'Paramètres';
    }
  }

  void _skipToContent() {
    widget.onSkipToContent?.call();
    final contentContext = _contentKey.currentContext;
    if (contentContext != null) {
      final focusScope = FocusScope.of(contentContext);
      final firstFocusable = focusScope.descendants.firstWhere(
        (node) => node.canRequestFocus,
        orElse: () => focusScope,
      );
      firstFocusable.requestFocus();
      
      if (widget.enableAccessibilityAnnouncements) {
        SemanticsService.announce(
          'Contenu principal atteint',
          TextDirection.ltr,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;
    
    // Use the new responsive breakpoints system
    if (context.isMobile) {
      content = _buildMobileLayout(context);
    } else {
      content = _buildDesktopLayout(context);
    }

    // Wrap with keyboard shortcuts provider if enabled
    if (widget.enableKeyboardShortcuts) {
      content = KeyboardShortcutsProvider(
        context: 'navigation',
        additionalCallbacks: {
          AppShortcut.home: () => _navigateToItem(NavigationItem.home),
          AppShortcut.guides: () => _navigateToItem(NavigationItem.guide),
          AppShortcut.quiz: () => _navigateToItem(NavigationItem.quiz),
          AppShortcut.tools: () => _navigateToItem(NavigationItem.tools),
        },
        child: content,
      );
    }

    // Add semantic wrapper for navigation landmark
    return Semantics(
      label: widget.semanticLabel ?? 'Navigation principale',
      container: true,
      explicitChildNodes: true,
      child: content,
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // Skip link for desktop navigation
        if (widget.enableSkipLinks) _buildSkipLink(context),
        _buildNavigationRail(context),
        Expanded(
          child: Container(
            key: _contentKey,
            child: widget.child,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Skip link for mobile navigation
          if (widget.enableSkipLinks) _buildSkipLink(context),
          Expanded(
            child: Container(
              key: _contentKey,
              child: widget.child,
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(context),
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Semantics(
      label: 'Navigation principale',
      container: true,
      child: BottomNavigationBar(
        currentIndex: _getIndexForItem(widget.selectedItem),
        onTap: (index) {
          final item = _getItemForIndex(index);
          widget.onNavigationItemSelected(item);
          _announceNavigation(item);
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
        items: [
          _buildBottomNavItem(Icons.home_rounded, 'Accueil', NavigationItem.home),
          _buildBottomNavItem(Icons.account_balance_rounded, 'PCG', NavigationItem.planComptable),
          _buildBottomNavItem(Icons.menu_book_rounded, 'Guide', NavigationItem.guide),
          _buildBottomNavItem(Icons.calculate_rounded, 'Outils', NavigationItem.tools),
          _buildBottomNavItem(Icons.quiz_rounded, 'Quiz', NavigationItem.quiz),
          _buildBottomNavItem(Icons.settings_rounded, 'Paramètres', NavigationItem.settings),
        ],
      ),
    );
  }

  BottomNavigationBarItem _buildBottomNavItem(IconData icon, String label, NavigationItem item) {
    final isSelected = widget.selectedItem == item;
    
    return BottomNavigationBarItem(
      icon: Semantics(
        label: '$label${isSelected ? ', sélectionné' : ''}',
        button: true,
        selected: isSelected,
        child: Icon(
          icon,
          semanticLabel: label,
        ),
      ),
      label: label,
      tooltip: 'Naviguer vers $label',
    );
  }

  Widget _buildSkipLink(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Container(
      height: 0, // Take no space when not focused
      child: OverflowBox(
        minHeight: 0,
        maxHeight: 48,
        alignment: Alignment.topLeft,
      child: Focus(
        focusNode: _skipLinkFocusNode,
        onFocusChange: (hasFocus) {
          // Move skip link into view when focused
          setState(() {});
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.translationValues(
            0,
            _skipLinkFocusNode.hasFocus ? 116 : 0, // Move down when focused
            0,
          ),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.space4,
                vertical: DesignTokens.space2,
              ),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
                border: Border.all(
                  color: colorScheme.primary,
                  width: 2,
                ),
              ),
              child: InkWell(
                onTap: _skipToContent,
                borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
                child: Padding(
                  padding: const EdgeInsets.all(DesignTokens.space1),
                  child: Text(
                    'Aller au contenu principal',
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      ),
    );
  }

  // Helper method to convert NavigationItem to bottom nav index
  int _getIndexForItem(NavigationItem item) {
    switch (item) {
      case NavigationItem.home:
        return 0;
      case NavigationItem.planComptable:
        return 1;
      case NavigationItem.guide:
        return 2;
      case NavigationItem.tools:
        return 3;
      case NavigationItem.quiz:
        return 4;
      case NavigationItem.settings:
        return 5;
      // References is not included in bottom navigation
      default:
        return 0;
    }
  }

  // Helper method to convert bottom nav index to NavigationItem
  NavigationItem _getItemForIndex(int index) {
    switch (index) {
      case 0:
        return NavigationItem.home;
      case 1:
        return NavigationItem.planComptable;
      case 2:
        return NavigationItem.guide;
      case 3:
        return NavigationItem.tools;
      case 4:
        return NavigationItem.quiz;
      case 5:
        return NavigationItem.settings;
      default:
        return NavigationItem.home;
    }
  }

  Widget _buildNavigationRail(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AccessibleFocusTraversalGroup(
      enableDirectionalNavigation: true,
      enableFocusRestoration: widget.enableFocusRestoration,
      enableAccessibilityAnnouncements: widget.enableAccessibilityAnnouncements,
      semanticLabel: 'Navigation latérale',
      landmarkRole: 'navigation',
      groupId: 'navigation_rail',
      restoreKey: 'navigation_rail_focus',
      child: Container(
        width: context.responsiveNavigationRailWidth,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: DesignTokens.elevation8,
              offset: const Offset(2, 0),
            ),
          ],
        ),
        child: Semantics(
          label: 'Navigation principale',
          container: true,
          explicitChildNodes: true,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
              SizedBox(height: DesignTokens.space6),
              _buildNavItem(
                context: context,
                icon: Icons.home_outlined,
                selectedIcon: Icons.home_rounded,
                label: 'Accueil',
                item: NavigationItem.home,
              ),
              SizedBox(height: DesignTokens.space2),
              _buildDivider(context),
              const SizedBox(height: 8),
              _buildNavItem(
                context: context,
                icon: Icons.account_balance_outlined,
                selectedIcon: Icons.account_balance_rounded,
                label: 'PCG',
                item: NavigationItem.planComptable,
              ),
              const SizedBox(height: 16),
              _buildNavItem(
                context: context,
                icon: Icons.menu_book_outlined,
                selectedIcon: Icons.menu_book_rounded,
                label: 'Guide',
                item: NavigationItem.guide,
              ),
              const SizedBox(height: 16),
              _buildNavItem(
                context: context,
                icon: Icons.calculate_outlined,
                selectedIcon: Icons.calculate_rounded,
                label: 'Outils',
                item: NavigationItem.tools,
              ),
              const SizedBox(height: 16),
              _buildNavItem(
                context: context,
                icon: Icons.library_books_outlined,
                selectedIcon: Icons.library_books_rounded,
                label: 'Références',
                item: NavigationItem.references,
              ),
              const SizedBox(height: 16),
              _buildNavItem(
                context: context,
                icon: Icons.quiz_outlined,
                selectedIcon: Icons.quiz_rounded,
                label: 'Quiz',
                item: NavigationItem.quiz,
              ),
              const SizedBox(height: 32),
              _buildDivider(context),
              const SizedBox(height: 8),
              _buildNavItem(
                context: context,
                icon: Icons.settings_outlined,
                selectedIcon: Icons.settings_rounded,
                label: 'Paramètres',
                item: NavigationItem.settings,
              ),
              const SizedBox(height: 24),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required IconData selectedIcon,
    required String label,
    required NavigationItem item,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = widget.selectedItem == item;
    final focusNode = _navigationFocusNodes[item]!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Focus(
        focusNode: focusNode,
        onKeyEvent: (node, event) => _handleNavItemKeyEvent(event, item),
        child: Builder(
          builder: (context) {
            final isFocused = Focus.of(context).hasFocus;
            
            return Semantics(
              label: '$label${isSelected ? ', page actuelle' : ''}',
              button: true,
              selected: isSelected,
              focused: isFocused,
              onTap: () => _navigateToItem(item),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                child: InkWell(
                  borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                  onTap: () => _navigateToItem(item),
                  focusColor: colorScheme.primary.withValues(alpha: 0.1),
                  hoverColor: colorScheme.primary.withValues(alpha: 0.05),
                  child: Container(
                    constraints: const BoxConstraints(
                      minHeight: 48, // Ensure minimum touch target size
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignTokens.space4,
                      vertical: DesignTokens.space3,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                      color: isSelected
                          ? colorScheme.primaryContainer.withValues(alpha: 0.2)
                          : Colors.transparent,
                      border: Border.all(
                        color: isFocused
                            ? colorScheme.primary
                            : isSelected
                                ? colorScheme.primary.withValues(alpha: 0.2)
                                : Colors.transparent,
                        width: isFocused ? 2.5 : 1.5,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(DesignTokens.space2),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? colorScheme.primary.withValues(alpha: 0.1)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
                          ),
                          child: Icon(
                            isSelected ? selectedIcon : icon,
                            color: isSelected
                                ? colorScheme.primary
                                : colorScheme.onSurfaceVariant,
                            size: DesignTokens.iconBase,
                            semanticLabel: label,
                          ),
                        ),
                        SizedBox(width: DesignTokens.space4),
                        Expanded(
                          child: Text(
                            label,
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: isSelected
                                  ? colorScheme.primary
                                  : colorScheme.onSurfaceVariant,
                              fontWeight:
                                  isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                          ),
                        ),
                        if (isSelected)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                        if (isFocused)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            width: 4,
                            height: 24,
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  KeyEventResult _handleNavItemKeyEvent(KeyEvent event, NavigationItem item) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    // Handle Enter and Space for activation
    if (event.logicalKey == LogicalKeyboardKey.enter ||
        event.logicalKey == LogicalKeyboardKey.space) {
      _navigateToItem(item);
      return KeyEventResult.handled;
    }

    // Handle arrow key navigation
    if (event.logicalKey == LogicalKeyboardKey.arrowUp ||
        event.logicalKey == LogicalKeyboardKey.arrowDown) {
      _handleArrowNavigation(event.logicalKey == LogicalKeyboardKey.arrowUp, item);
      return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }

  void _handleArrowNavigation(bool isUp, NavigationItem currentItem) {
    final items = [
      NavigationItem.home,
      NavigationItem.planComptable,
      NavigationItem.guide,
      NavigationItem.tools,
      NavigationItem.references,
      NavigationItem.quiz,
      NavigationItem.settings,
    ];

    final currentIndex = items.indexOf(currentItem);
    if (currentIndex == -1) return;

    int nextIndex;
    if (isUp) {
      nextIndex = currentIndex == 0 ? items.length - 1 : currentIndex - 1;
    } else {
      nextIndex = currentIndex == items.length - 1 ? 0 : currentIndex + 1;
    }

    final nextItem = items[nextIndex];
    _focusNavigationItem(nextItem);
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: DesignTokens.space6),
      child: Divider(
        color: Theme.of(context).colorScheme.outlineVariant.withValues(alpha: 0.5),
        height: 1,
      ),
    );
  }
}
